<script setup lang="ts">
// Hero section with video background for AI video generation
const { t } = useI18n()
</script>

<template>
  <section class="relative h-screen flex items-center justify-center overflow-hidden">
    <!-- Video Background -->
    <div class="absolute inset-0 z-0">
      <video
        autoplay
        muted
        loop
        playsinline
        class="w-full h-full object-cover"
      >
        <source
          src="https://cdn.heyvideo.ai/demo-2.mp4"
          type="video/mp4"
        >
        <!-- Fallback image if video doesn't load -->
        <img
          src="https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/hero-bg.jpg"
          alt="AI Video Generation Background"
          class="w-full h-full object-cover"
        >
      </video>
      <!-- Dark overlay for better text readability -->
      <div class="absolute inset-0 bg-black/50" />
    </div>

    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white max-w-4xl mx-auto px-6">
      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.2
        }"
      >
        <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
          {{ t('hero.title', 'Tạo Video AI Chất Lượng Cao') }}
        </h1>
      </Motion>

      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.4
        }"
      >
        <p class="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
          {{ t('hero.subtitle', 'Chuyển đổi ý tưởng thành video AI đáng kinh ngạc chỉ trong vài giây. Công nghệ tiên tiến nhất để tạo nội dung video chuyên nghiệp.') }}
        </p>
      </Motion>

      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.6
        }"
      >
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <UButton
            size="xl"
            color="primary"
            variant="solid"
            :label="t('hero.cta.primary', 'Bắt Đầu Tạo Video')"
            to="/app"
            class="px-8 py-4 text-lg font-semibold"
            icon="mingcute:ai-fill"
          />
          <UButton
            size="xl"
            color="white"
            variant="outline"
            :label="t('hero.cta.secondary', 'Xem Demo')"
            class="px-8 py-4 text-lg font-semibold border-white text-white hover:bg-white hover:text-black"
            icon="lucide:play"
            @click="scrollToDemo"
          />
        </div>
      </Motion>

      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.8
        }"
      >
        <div class="mt-12 text-sm text-gray-300">
          <p>{{ t('hero.users', 'Được tin tưởng bởi hơn 10,000+ người sáng tạo trên toàn thế giới') }}</p>
        </div>
      </Motion>
    </div>

    <!-- Scroll indicator -->
    <Motion
      :initial="{
        opacity: 0,
        y: -20
      }"
      :animate="{
        opacity: 1,
        y: 0
      }"
      :transition="{
        duration: 1,
        delay: 1.2,
        repeat: Infinity,
        repeatType: 'reverse'
      }"
      class="absolute bottom-8 left-1/2 transform -translate-x-1/2"
    >
      <div class="flex flex-col items-center text-white/70">
        <span class="text-sm mb-2">{{ t('hero.scroll', 'Cuộn để khám phá') }}</span>
        <UIcon
          name="lucide:chevron-down"
          class="w-6 h-6"
        />
      </div>
    </Motion>
  </section>
</template>

<script lang="ts">
export default {
  methods: {
    scrollToDemo() {
      const demoSection = document.getElementById('demo-section')
      if (demoSection) {
        demoSection.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }
}
</script>

<style scoped>
/* Ensure video covers the entire section */
video {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

/* Additional responsive adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  p {
    font-size: 1.125rem;
  }
}
</style>
